name: Deploy to GitHub Pages

on:
  push:
    branches:
      - main  # Set this to the branch you want to deploy from

# Sets permissions of the GITHUB_TOKEN to allow deployment to GitHub Pages
permissions:
  contents: write
  pages: write
  id-token: write

jobs:
  # Deploy job - pushes content to gh-pages branch
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Deploy to GitHub Pages
        uses: JamesIves/github-pages-deploy-action@v4
        with:
          folder: .  # The folder the action should deploy
          branch: gh-pages  # The branch the action should deploy to
          clean: true  # Automatically remove deleted files from the deploy branch
