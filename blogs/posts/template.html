<!DOCTYPE html>
<html lang="en">
<head>
<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-WXYSSSTTMB"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'G-WXYSSSTTMB');
</script>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Blog Post Title - Meet <PERSON>'s Blog</title>
    <link rel="stylesheet" href="../assets/css/tech-theme.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Fira+Code&display=swap" rel="stylesheet">
    <!-- Add syntax highlighting for code blocks -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container nav-container">
            <a href="../index.html" class="logo">
                <span class="logo-icon"><i class="fas fa-code"></i></span>
                Meet Shah
            </a>
            <div class="nav-links">
                <a href="../index.html" class="nav-link active">Blog</a>
                <a href="https://shahmeetk.github.io" class="nav-link">Portfolio</a>
                <a href="https://github.com/shahmeetk" class="nav-link" target="_blank">GitHub</a>
                <a href="https://www.linkedin.com/in/meetshah10290/" class="nav-link" target="_blank">LinkedIn</a>
            </div>
        </div>
    </nav>

    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content animate-fade-in">
                <h1 class="header-title">Your Blog Post Title</h1>
                <p class="header-subtitle">Your blog post subtitle or brief description</p>
                <div class="tech-badge">
                    <i class="fas fa-code tech-badge-icon"></i>
                    Category Badge
                </div>
            </div>
        </div>
    </header>

    <main class="container">
        <a href="../index.html" class="back-link">
            <i class="fas fa-arrow-left"></i> Back to Blog
        </a>

        <article class="blog-post animate-fade-in">
            <div class="blog-post-meta">
                <div class="blog-post-meta-item">
                    <i class="far fa-calendar-alt"></i>
                    Month Day, Year
                </div>
                <div class="blog-post-meta-item">
                    <i class="far fa-user"></i>
                    Meet Shah
                </div>
                <div class="blog-post-meta-item">
                    <i class="far fa-clock"></i>
                    X min read
                </div>
            </div>

            <div class="tags">
                <a href="#" class="tag"><i class="fas fa-tag"></i> Tag 1</a>
                <a href="#" class="tag"><i class="fas fa-tag"></i> Tag 2</a>
                <a href="#" class="tag"><i class="fas fa-tag"></i> Tag 3</a>
                <a href="#" class="tag"><i class="fas fa-tag"></i> Tag 4</a>
            </div>

            <!-- Optional Project Overview Box -->
            <div class="project-overview">
                <h3><i class="fas fa-info-circle"></i> Project Overview</h3>
                <p>A brief overview of your project or topic.</p>
                <a href="#" target="_blank" class="btn primary-btn">
                    <i class="fab fa-github"></i> View on GitHub
                </a>
            </div>

            <div class="blog-post-content">
                <h2>Introduction</h2>
                <p>Introduction paragraph that explains the topic and why it's important. This should capture the reader's attention and provide context for the rest of the article.</p>

                <p>Second paragraph that expands on the introduction and sets up the main content of the blog post.</p>

                <h2>First Main Section</h2>

                <p>Content for the first main section. This could be background information, a problem statement, or the beginning of a tutorial.</p>

                <ul>
                    <li><strong>Important point 1:</strong> Description or explanation</li>
                    <li><strong>Important point 2:</strong> Description or explanation</li>
                    <li><strong>Important point 3:</strong> Description or explanation</li>
                </ul>

                <p>Additional content for this section.</p>

                <h2>Second Main Section</h2>

                <p>Content for the second main section.</p>

                <h3>Subsection 1</h3>

                <p>Content for the first subsection.</p>

                <!-- Example of including an image -->
                <div class="image-container">
                    <img src="path/to/your/image.jpg" alt="Description of the image" class="blog-image">
                    <p class="image-caption">Caption for the image</p>
                </div>

                <h3>Subsection 2</h3>

                <p>Content for the second subsection.</p>

                <!-- Example of including code -->
                <div class="code-header">
                    <span class="code-language">language</span>
                    <button class="code-copy" title="Copy to clipboard"><i class="far fa-copy"></i></button>
                </div>
                <pre><code class="language-javascript">// Example code
function exampleFunction() {
    console.log("Hello, world!");
}</code></pre>

                <div class="tech-tip">
                    <p>This is a technical tip or important note that you want to highlight for the reader. It can include best practices, warnings, or additional context.</p>
                </div>

                <h2>Third Main Section</h2>

                <p>Content for the third main section.</p>

                <ol>
                    <li>First step or point</li>
                    <li>Second step or point</li>
                    <li>Third step or point</li>
                </ol>

                <p>Additional content for this section.</p>

                <h2>Conclusion</h2>

                <p>Summary of the key points discussed in the blog post.</p>

                <p>Final thoughts and potential next steps or future developments.</p>

                <div class="share-section">
                    <h3 class="share-title">Share this article</h3>
                    <div class="share-buttons">
                        <a href="#" class="share-button twitter" title="Share on Twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="share-button linkedin" title="Share on LinkedIn">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                        <a href="#" class="share-button facebook" title="Share on Facebook">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="share-button email" title="Share via Email">
                            <i class="far fa-envelope"></i>
                        </a>
                    </div>
                </div>
            </div>
        </article>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <a href="../index.html" class="footer-logo">
                    <i class="fas fa-code"></i>
                    Meet Shah
                </a>

                <div class="footer-links">
                    <a href="../index.html" class="footer-link">Blog</a>
                    <a href="https://shahmeetk.github.io" class="footer-link">Portfolio</a>
                    <a href="#" class="footer-link">About</a>
                    <a href="mailto:<EMAIL>" class="footer-link">Contact</a>
                </div>

                <div class="footer-social">
                    <a href="https://github.com/shahmeetk" class="social-link" target="_blank">
                        <i class="fab fa-github"></i>
                    </a>
                    <a href="https://www.linkedin.com/in/meetshah10290/" class="social-link" target="_blank">
                        <i class="fab fa-linkedin-in"></i>
                    </a>
                    <a href="mailto:<EMAIL>" class="social-link">
                        <i class="far fa-envelope"></i>
                    </a>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="copyright">
                    &copy; 2024 Meet Shah. All rights reserved.
                </div>
                <div>
                    <span class="tech-badge">
                        <i class="fas fa-code tech-badge-icon"></i>
                        Tech Content
                    </span>
                </div>
            </div>
        </div>
    </footer>

    <!-- Syntax highlighting for code blocks -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-javascript.min.js"></script>
    <!-- Add additional language components as needed -->

    <!-- Copy code functionality -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Copy code functionality
            const copyButtons = document.querySelectorAll('.code-copy');
            copyButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const codeBlock = this.parentElement.nextElementSibling.querySelector('code');
                    const textToCopy = codeBlock.textContent;

                    navigator.clipboard.writeText(textToCopy).then(() => {
                        // Change button icon temporarily
                        const originalIcon = this.innerHTML;
                        this.innerHTML = '<i class="fas fa-check"></i>';
                        setTimeout(() => {
                            this.innerHTML = originalIcon;
                        }, 2000);
                    });
                });
            });
        });
    </script>
</body>
</html>
