/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #1e3a8a;
    --primary-dark: #1e40af;
    --secondary-color: #3b82f6;
    --accent-color: #0ea5e9;
    --issi-blue: #1e3a8a;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #ffffff;
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-dark: #0f172a;
    --border-color: #e2e8f0;
    --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.2);
    --gradient-primary: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    --gradient-hero: linear-gradient(135deg, rgba(30, 58, 138, 0.95) 0%, rgba(59, 130, 246, 0.9) 100%);
    --border-radius: 8px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: var(--transition);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo-img {
    height: 40px;
    width: auto;
}

.logo-text {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--issi-blue);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
    align-items: center;
}

.nav-menu a {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.nav-menu a:hover {
    color: var(--primary-color);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: var(--transition);
}

.nav-menu a:hover::after {
    width: 100%;
}

.cta-nav {
    background: var(--gradient-primary);
    color: var(--text-light) !important;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-weight: 600;
}

.cta-nav:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    transition: var(--transition);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background: var(--gradient-hero);
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('https://images.unsplash.com/photo-1677442136019-21780ecad995?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80') center/cover;
    z-index: -1;
}

.hero-content {
    text-align: center;
    color: var(--text-light);
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 0;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    padding: 0.5rem 1rem;
    border-radius: 50px;
    margin-bottom: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.hero-logo {
    height: 24px;
    width: auto;
    filter: brightness(0) invert(1);
}

.hero-badge span {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-light);
}

.hero h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    line-height: 1.2;
}

.hero p {
    font-size: clamp(1.1rem, 2vw, 1.3rem);
    margin-bottom: 2rem;
    opacity: 0.95;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin: 3rem 0;
    flex-wrap: wrap;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--accent-color);
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Button Styles */
.cta-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    text-decoration: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: 1.1rem;
    transition: var(--transition);
    cursor: pointer;
    border: none;
}

.cta-button.primary {
    background: var(--text-light);
    color: var(--primary-color);
}

.cta-button.primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-heavy);
}

.cta-button.secondary {
    background: transparent;
    color: var(--text-light);
    border: 2px solid var(--text-light);
}

.cta-button.secondary:hover {
    background: var(--text-light);
    color: var(--primary-color);
}

/* Section Styles */
.section {
    padding: 5rem 0;
}

.section-light {
    background: var(--bg-secondary);
}

.section-dark {
    background: var(--bg-dark);
    color: var(--text-light);
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    margin-bottom: 1rem;
    color: inherit;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

.section-dark .section-subtitle {
    color: rgba(255, 255, 255, 0.8);
}

/* Grid Layouts */
.grid-2 {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
    align-items: start;
}

.grid-3 {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

/* Card Styles */
.info-card {
    background: var(--bg-primary);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
}

.info-card-header h3 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
}

.info-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
}

.info-item:last-child {
    border-bottom: none;
}

.outcome-card, .benefit-card {
    background: var(--bg-primary);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    text-align: center;
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.outcome-card:hover, .benefit-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.outcome-icon, .benefit-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

.outcome-card h3, .benefit-card h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

/* Content Blocks */
.content-block h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.checkmark-list {
    list-style: none;
    margin-top: 1.5rem;
}

.checkmark-list li {
    position: relative;
    padding-left: 2rem;
    margin-bottom: 0.5rem;
}

.checkmark-list li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #28a745;
    font-weight: bold;
    font-size: 1.2rem;
}

/* Agenda Timeline */
.agenda-timeline {
    max-width: 800px;
    margin: 0 auto;
}

.agenda-item {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    border-left: 4px solid var(--primary-color);
}

.agenda-item.break {
    border-left-color: var(--accent-color);
    background: rgba(64, 224, 208, 0.1);
}

.agenda-time {
    flex: 0 0 120px;
    font-weight: 700;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.agenda-content h4 {
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    font-size: 1.2rem;
}

.agenda-content p {
    color: var(--text-secondary);
}

/* Registration Section */
.registration-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    max-width: 1000px;
    margin: 0 auto;
}

.registration-form {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: var(--border-radius);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-light);
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--border-radius);
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-light);
    font-size: 1rem;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(64, 224, 208, 0.2);
}

.form-group input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.register-button {
    width: 100%;
    background: var(--accent-color);
    color: var(--bg-dark);
    padding: 1rem 2rem;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 700;
    font-size: 1.1rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.register-button:hover {
    background: #36c7b8;
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.registration-info {
    color: var(--text-light);
}

.registration-info h3 {
    margin-bottom: 2rem;
    font-size: 1.5rem;
}

.step {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.step-number {
    flex: 0 0 40px;
    height: 40px;
    background: var(--accent-color);
    color: var(--bg-dark);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
}

.step-content h4 {
    margin-bottom: 0.5rem;
    color: var(--text-light);
}

.step-content p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

/* FAQ Section */
.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background: var(--bg-primary);
    margin-bottom: 1rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.faq-question {
    padding: 1.5rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    color: var(--text-primary);
    transition: var(--transition);
}

.faq-question:hover {
    background: var(--bg-secondary);
}

.faq-toggle {
    font-size: 1.5rem;
    color: var(--primary-color);
    transition: var(--transition);
}

.faq-item.active .faq-toggle {
    transform: rotate(45deg);
}

.faq-answer {
    padding: 0 1.5rem;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item.active .faq-answer {
    padding: 0 1.5rem 1.5rem;
    max-height: 200px;
}

.faq-answer p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Footer */
.footer {
    background: var(--bg-dark);
    color: var(--text-light);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 3rem;
    margin-bottom: 2rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.footer-logo-img {
    height: 32px;
    width: auto;
    filter: brightness(0) invert(1);
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: var(--accent-color);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
}

.footer-section a:hover {
    color: var(--accent-color);
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.social-links a:hover {
    background: var(--accent-color);
    color: var(--bg-dark);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: rgba(255, 255, 255, 0.6);
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-bottom-left,
.footer-bottom-right {
    flex: 1;
}

.footer-bottom-right {
    text-align: right;
}

.footer-bottom a {
    color: var(--accent-color);
    text-decoration: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        display: none;
    }

    .hero-stats {
        gap: 2rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .cta-button {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .registration-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .agenda-item {
        flex-direction: column;
        gap: 1rem;
    }

    .agenda-time {
        flex: none;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-bottom {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .footer-bottom-right {
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    .section {
        padding: 3rem 0;
    }
    
    .hero {
        min-height: 80vh;
    }
    
    .hero-content {
        padding: 1rem 0;
    }
}

/* Animation Classes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Focus Styles for Accessibility */
*:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}
