# AI Transformation Workshop - Cloudflare Pages Deployment

This is an optimized landing page for the AI Transformation Workshop, designed for deployment on Cloudflare Pages.

## Features

- **Responsive Design**: Mobile-first approach with modern CSS Grid and Flexbox
- **Performance Optimized**: Minified assets, lazy loading, and efficient animations
- **SEO Friendly**: Meta tags, Open Graph, and structured data
- **Analytics Integration**: Google Analytics (G-WXYSSSTTMB) with event tracking
- **Interactive Elements**: FAQ accordion, smooth scrolling, form validation
- **Accessibility**: WCAG compliant with proper focus management
- **Security Headers**: CSP, XSS protection, and other security measures

## File Structure

```
cloudflare-deployment/
├── index.html              # Main landing page
├── assets/
│   ├── css/
│   │   └── styles.css      # Compiled and optimized CSS
│   ├── js/
│   │   └── main.js         # Interactive functionality
│   └── images/             # Optimized images (if any)
├── _headers                # Cloudflare Pages headers configuration
├── _redirects              # URL redirects and rewrites
└── README.md               # This file
```

## Deployment Instructions

### Method 1: Direct Upload (Recommended)

1. **Create a ZIP file** of the entire `cloudflare-deployment` folder
2. **Log in to Cloudflare Dashboard**
3. **Go to Pages** section
4. **Click "Create a project"**
5. **Choose "Upload assets"**
6. **Upload the ZIP file**
7. **Configure custom domain** (optional)

### Method 2: Git Integration

1. **Push this folder to a Git repository**
2. **Connect Cloudflare Pages to your repository**
3. **Set build settings**:
   - Build command: (leave empty)
   - Build output directory: `/`
   - Root directory: `cloudflare-deployment`

## Configuration

### Custom Domain Setup

1. In Cloudflare Pages dashboard, go to your project
2. Click "Custom domains" tab
3. Add your domain (e.g., `ai-workshop.yourdomain.com`)
4. Update DNS records as instructed

### Environment Variables

No environment variables are required for this static site.

### Analytics Setup

The site includes Google Analytics with ID `G-WXYSSSTTMB`. To use your own:

1. Replace `G-WXYSSSTTMB` in `index.html` with your GA4 measurement ID
2. Update the tracking configuration as needed

## Performance Features

- **Optimized Images**: WebP format with fallbacks
- **Minified Assets**: CSS and JS are optimized for production
- **Caching Headers**: Aggressive caching for static assets
- **Lazy Loading**: Images load only when needed
- **Critical CSS**: Above-the-fold styles inlined

## Browser Support

- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+
- Mobile browsers (iOS Safari 12+, Chrome Mobile 60+)

## SEO Optimization

- Meta descriptions and keywords
- Open Graph tags for social sharing
- Twitter Card support
- Structured data markup
- Semantic HTML structure
- Fast loading times

## Security Features

- Content Security Policy headers
- XSS protection
- Clickjacking protection
- HTTPS enforcement
- Secure cookie settings

## Contact Information

For questions about the workshop or technical issues:

- **Email**: <EMAIL>
- **Contact Form**: https://www.issi-inc.com/contactus
- **Developer**: Meet Shah (https://www.linkedin.com/in/meetshah10290/)

## License

© 2025 Microsoft Partner AI Workshop. All Rights Reserved.

---

**Deployment Date**: January 2025  
**Version**: 1.0  
**Optimized for**: Cloudflare Pages
